import React from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import DataTable from '@/Components/Admin/DataTable';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  KeyIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  email: string;
  email_verified_at: string | null;
  created_at: string;
  groups: Array<{
    id: string;
    name: string;
  }>;
  api_keys_count: number;
  request_logs_count: number;
}

interface UsersIndexProps {
  users: User[];
}

export default function UsersIndex({ users }: UsersIndexProps) {
  const handleDelete = (user: User) => {
    if (confirm(`Are you sure you want to delete user "${user.name}"?`)) {
      router.delete(`/admin/users/${user.id}`, {
        onSuccess: () => {
          // Handle success if needed
        },
        onError: (errors) => {
          console.error('Delete failed:', errors);
        },
      });
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (value: string, row: User) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8">
            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700">
                {value.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{row.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'groups',
      label: 'Groups',
      render: (groups: User['groups']) => (
        <div className="flex flex-wrap gap-1">
          {groups.length > 0 ? (
            groups.map((group) => (
              <span
                key={group.id}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                <UserGroupIcon className="h-3 w-3 mr-1" />
                {group.name}
              </span>
            ))
          ) : (
            <span className="text-sm text-gray-500">No groups assigned</span>
          )}
        </div>
      ),
    },
    {
      key: 'api_keys_count',
      label: 'API Keys',
      render: (value: number) => (
        <div className="flex items-center">
          <KeyIcon className="h-4 w-4 text-gray-400 mr-1" />
          <span className="text-sm text-gray-900">{value}</span>
        </div>
      ),
    },
    {
      key: 'request_logs_count',
      label: 'Requests',
      render: (value: number) => (
        <span className="text-sm text-gray-900">{value.toLocaleString()}</span>
      ),
    },
    {
      key: 'email_verified_at',
      label: 'Status',
      render: (value: string | null) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value
              ? 'bg-green-100 text-green-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}
        >
          {value ? 'Verified' : 'Unverified'}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value: string) => (
        <span className="text-sm text-gray-500">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: User) => (
        <div className="flex items-center space-x-2">
          <Link
            href={`/admin/users/${row.id}`}
            className="text-blue-600 hover:text-blue-900"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </Link>
          <Link
            href={`/admin/users/${row.id}/edit`}
            className="text-indigo-600 hover:text-indigo-900"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </Link>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { name: 'Users', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Users
            </h1>
          </div>
          <Link
            href="/admin/users/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add User
          </Link>
        </div>
      }
    >
      <Head title="Users" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <DataTable
            columns={columns}
            data={users}
            searchPlaceholder="Search users..."
          />
        </div>
      </div>
    </AdminLayout>
  );
}
