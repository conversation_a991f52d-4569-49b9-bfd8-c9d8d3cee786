import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface Group {
  id: string;
  name: string;
  description?: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  groups: Array<{
    id: string;
    name: string;
  }>;
}

interface EditUserProps {
  user: User;
  groups: Group[];
}

interface FormData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  groups: string[];
}

export default function EditUser({ user, groups }: EditUserProps) {
  const { data, setData, put, processing, errors } = useForm<FormData>({
    name: user.name,
    email: user.email,
    password: '',
    password_confirmation: '',
    groups: user.groups.map(group => group.id),
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/admin/users/${user.id}`);
  };

  const handleGroupToggle = (groupId: string) => {
    const currentGroups = [...data.groups];
    const index = currentGroups.indexOf(groupId);
    
    if (index > -1) {
      currentGroups.splice(index, 1);
    } else {
      currentGroups.push(groupId);
    }
    
    setData('groups', currentGroups);
  };

  const breadcrumbItems = [
    { name: 'Users', href: '/admin/users' },
    { name: user.name, href: `/admin/users/${user.id}` },
    { name: 'Edit', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center space-x-4">
          <Link
            href={`/admin/users/${user.id}`}
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </Link>
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Edit User: {user.name}
            </h1>
          </div>
        </div>
      }
    >
      <Head title={`Edit User: ${user.name}`} />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={data.name}
                  onChange={(e) => setData('name', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>
            </div>

            {/* Password */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  New Password (Optional)
                </label>
                <input
                  type="password"
                  id="password"
                  value={data.password}
                  onChange={(e) => setData('password', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Leave blank to keep current password
                </p>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                )}
              </div>

              <div>
                <label htmlFor="password_confirmation" className="block text-sm font-medium text-gray-700">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  id="password_confirmation"
                  value={data.password_confirmation}
                  onChange={(e) => setData('password_confirmation', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                {errors.password_confirmation && (
                  <p className="mt-1 text-sm text-red-600">{errors.password_confirmation}</p>
                )}
              </div>
            </div>

            {/* Groups */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Groups
              </label>
              <div className="space-y-2">
                {groups.map((group) => (
                  <div key={group.id} className="flex items-center">
                    <input
                      id={`group-${group.id}`}
                      type="checkbox"
                      checked={data.groups.includes(group.id)}
                      onChange={() => handleGroupToggle(group.id)}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor={`group-${group.id}`} className="ml-3 block text-sm text-gray-700">
                      <span className="font-medium">{group.name}</span>
                      {group.description && (
                        <span className="text-gray-500 ml-2">- {group.description}</span>
                      )}
                    </label>
                  </div>
                ))}
              </div>
              {errors.groups && (
                <p className="mt-1 text-sm text-red-600">{errors.groups}</p>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3">
              <Link
                href={`/admin/users/${user.id}`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {processing ? 'Updating...' : 'Update User'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
