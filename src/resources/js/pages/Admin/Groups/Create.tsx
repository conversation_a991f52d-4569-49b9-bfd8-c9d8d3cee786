import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  provider: {
    id: string;
    name: string;
    type: string;
  };
}

interface CreateGroupProps {
  models: LlmModel[];
}

interface FormData {
  name: string;
  description: string;
  is_active: boolean;
  has_all_models_access: boolean;
  model_permissions: Array<{
    model_id: string;
    can_access: boolean;
    restrictions: string[];
  }>;
}

export default function CreateGroup({ models }: CreateGroupProps) {
  const { data, setData, post, processing, errors } = useForm<FormData>({
    name: '',
    description: '',
    is_active: true,
    has_all_models_access: false,
    model_permissions: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/admin/groups');
  };

  const handleModelToggle = (modelId: string) => {
    const currentPermissions = [...data.model_permissions];
    const existingIndex = currentPermissions.findIndex(p => p.model_id === modelId);
    
    if (existingIndex > -1) {
      currentPermissions.splice(existingIndex, 1);
    } else {
      currentPermissions.push({
        model_id: modelId,
        can_access: true,
        restrictions: [],
      });
    }
    
    setData('model_permissions', currentPermissions);
  };

  const isModelSelected = (modelId: string) => {
    return data.model_permissions.some(p => p.model_id === modelId);
  };

  const breadcrumbItems = [
    { name: 'Groups', href: '/admin/groups' },
    { name: 'Create', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center space-x-4">
          <Link
            href="/admin/groups"
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </Link>
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Create Group
            </h1>
          </div>
        </div>
      }
    >
      <Head title="Create Group" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={data.name}
                  onChange={(e) => setData('name', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <input
                  type="text"
                  id="description"
                  value={data.description}
                  onChange={(e) => setData('description', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                )}
              </div>
            </div>

            {/* Status */}
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  id="is_active"
                  type="checkbox"
                  checked={data.is_active}
                  onChange={(e) => setData('is_active', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-3 block text-sm text-gray-700">
                  Active
                </label>
              </div>
              {errors.is_active && (
                <p className="mt-1 text-sm text-red-600">{errors.is_active}</p>
              )}

              <div className="flex items-center">
                <input
                  id="has_all_models_access"
                  type="checkbox"
                  checked={data.has_all_models_access}
                  onChange={(e) => {
                    setData('has_all_models_access', e.target.checked);
                    // Clear individual model permissions when "all models" is selected
                    if (e.target.checked) {
                      setData('model_permissions', []);
                    }
                  }}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="has_all_models_access" className="ml-3 block text-sm text-gray-700">
                  <span className="font-medium">Access to All Models</span>
                  <span className="block text-xs text-gray-500">
                    Automatically grants access to all current and future models
                  </span>
                </label>
              </div>
              {errors.has_all_models_access && (
                <p className="mt-1 text-sm text-red-600">{errors.has_all_models_access}</p>
              )}
            </div>

            {/* Model Permissions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Model Access Permissions
              </label>
              {data.has_all_models_access ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">
                        All Models Access Enabled
                      </h3>
                      <div className="mt-1 text-sm text-green-700">
                        This group will have access to all current and future models automatically.
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {models.map((model) => (
                    <div key={model.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center">
                        <input
                          id={`model-${model.id}`}
                          type="checkbox"
                          checked={isModelSelected(model.id)}
                          onChange={() => handleModelToggle(model.id)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`model-${model.id}`} className="ml-3 block text-sm">
                          <div className="font-medium text-gray-900">{model.name}</div>
                          <div className="text-gray-500">
                            {model.model_identifier} • {model.provider.name}
                          </div>
                        </label>
                      </div>
                      <div className="text-sm text-gray-500">
                        {model.provider.type}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {errors.model_permissions && (
                <p className="mt-1 text-sm text-red-600">{errors.model_permissions}</p>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/admin/groups"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {processing ? 'Creating...' : 'Create Group'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
