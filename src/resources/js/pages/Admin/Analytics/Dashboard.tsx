import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import MetricCard from '@/Components/Admin/MetricCard';
import LineChart from '@/Components/Admin/Charts/LineChart';
import Bar<PERSON>hart from '@/Components/Admin/Charts/BarChart';
import Doughnut<PERSON>hart from '@/Components/Admin/Charts/DoughnutChart';
import DataTable from '@/Components/Admin/DataTable';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  CpuChipIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  overview: {
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    success_rate: number;
    active_users: number;
    active_models: number;
  };
  usage_by_model: Array<{
    name: string;
    model_identifier: string;
    provider_name: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
    avg_duration: number;
  }>;
  usage_by_provider: Array<{
    name: string;
    type: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
    avg_duration: number;
  }>;
  daily_usage: Array<{
    date: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
  }>;
  cost_breakdown: {
    by_model: Array<{
      name: string;
      total_cost: number;
      input_tokens: number;
      output_tokens: number;
    }>;
    by_user: Array<{
      name: string;
      email: string;
      total_cost: number;
      request_count: number;
    }>;
  };
  top_users: Array<{
    name: string;
    email: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
    last_request: string;
  }>;
  error_rates: {
    by_model: Array<{
      name: string;
      total_requests: number;
      error_count: number;
      error_rate: number;
    }>;
    by_endpoint: Array<{
      endpoint: string;
      total_requests: number;
      error_count: number;
      error_rate: number;
    }>;
  };
}

interface DashboardProps {
  analytics: AnalyticsData;
  period: string;
}

export default function Dashboard({ analytics, period }: DashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  const handlePeriodChange = (newPeriod: string) => {
    setSelectedPeriod(newPeriod);
    // In a real app, this would trigger a new request to the server
    window.location.href = `/admin/analytics?period=${newPeriod}`;
  };

  // Prepare chart data
  const dailyUsageChart = {
    labels: analytics.daily_usage.map(d => new Date(d.date).toLocaleDateString()),
    datasets: [
      {
        label: 'Requests',
        data: analytics.daily_usage.map(d => d.request_count),
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
      },
    ],
  };

  const modelUsageChart = {
    labels: analytics.usage_by_model.slice(0, 10).map(m => m.name),
    datasets: [
      {
        label: 'Requests',
        data: analytics.usage_by_model.slice(0, 10).map(m => m.request_count),
      },
    ],
  };

  const costBreakdownChart = {
    labels: analytics.cost_breakdown.by_model.slice(0, 8).map(m => m.name),
    datasets: [
      {
        data: analytics.cost_breakdown.by_model.slice(0, 8).map(m => m.total_cost),
      },
    ],
  };

  const modelTableColumns = [
    { key: 'name', label: 'Model', sortable: true },
    { key: 'provider_name', label: 'Provider', sortable: true },
    { 
      key: 'request_count', 
      label: 'Requests', 
      sortable: true,
      render: (value: number) => value.toLocaleString()
    },
    { 
      key: 'total_tokens', 
      label: 'Tokens', 
      sortable: true,
      render: (value: number) => value.toLocaleString()
    },
    { 
      key: 'total_cost', 
      label: 'Cost', 
      sortable: true,
      render: (value: number) => `$${value.toFixed(4)}`
    },
    { 
      key: 'avg_duration', 
      label: 'Avg Duration', 
      sortable: true,
      render: (value: number) => `${Math.round(value)}ms`
    },
  ];

  const userTableColumns = [
    { key: 'name', label: 'User', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    { 
      key: 'request_count', 
      label: 'Requests', 
      sortable: true,
      render: (value: number) => value.toLocaleString()
    },
    { 
      key: 'total_tokens', 
      label: 'Tokens', 
      sortable: true,
      render: (value: number) => value.toLocaleString()
    },
    { 
      key: 'total_cost', 
      label: 'Cost', 
      sortable: true,
      render: (value: number) => `$${value.toFixed(4)}`
    },
    { 
      key: 'last_request', 
      label: 'Last Request', 
      sortable: true,
      render: (value: string) => new Date(value).toLocaleDateString()
    },
  ];

  const breadcrumbItems = [
    { name: 'Analytics Dashboard', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Analytics Dashboard
            </h1>
          </div>
          <select
            value={selectedPeriod}
            onChange={(e) => handlePeriodChange(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="1d">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      }
    >
      <Head title="Analytics Dashboard" />

      <div className="space-y-6">
          {/* Overview Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <MetricCard
              title="Total Requests"
              value={analytics.overview.total_requests}
              icon={<ChartBarIcon />}
              color="blue"
            />
            <MetricCard
              title="Total Tokens"
              value={analytics.overview.total_tokens}
              icon={<CpuChipIcon />}
              color="green"
            />
            <MetricCard
              title="Total Cost"
              value={`$${analytics.overview.total_cost.toFixed(2)}`}
              icon={<CurrencyDollarIcon />}
              color="yellow"
            />
            <MetricCard
              title="Success Rate"
              value={`${analytics.overview.success_rate.toFixed(1)}%`}
              icon={<ExclamationTriangleIcon />}
              color={analytics.overview.success_rate >= 95 ? 'green' : 'red'}
            />
            <MetricCard
              title="Active Users"
              value={analytics.overview.active_users}
              icon={<UserGroupIcon />}
              color="purple"
            />
            <MetricCard
              title="Active Models"
              value={analytics.overview.active_models}
              icon={<ClockIcon />}
              color="indigo"
            />
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LineChart
              data={dailyUsageChart}
              title="Daily Usage Trend"
              height={300}
            />
            <DoughnutChart
              data={costBreakdownChart}
              title="Cost by Model"
              height={300}
            />
          </div>

          <BarChart
            data={modelUsageChart}
            title="Top Models by Request Count"
            height={300}
          />

          {/* Data Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Model Usage</h3>
              <DataTable
                columns={modelTableColumns}
                data={analytics.usage_by_model}
                searchPlaceholder="Search models..."
              />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Top Users</h3>
              <DataTable
                columns={userTableColumns}
                data={analytics.top_users}
                searchPlaceholder="Search users..."
              />
            </div>
          </div>
      </div>
    </AdminLayout>
  );
}
