import React from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface CreateProviderProps {
  // Add any props if needed
}

export default function Create({}: CreateProviderProps) {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    type: 'openai' as 'anthropic' | 'openai' | 'openai_compatible',
    base_url: '',
    api_key: '',
    is_active: true,
    config: {} as Record<string, any>,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/admin/providers');
  };

  const handleTypeChange = (type: 'anthropic' | 'openai' | 'openai_compatible') => {
    setData(prev => ({
      ...prev,
      type,
      base_url: getDefaultBaseUrl(type),
      config: getDefaultConfig(type),
    }));
  };

  const getDefaultBaseUrl = (type: string) => {
    switch (type) {
      case 'anthropic':
        return 'https://api.anthropic.com';
      case 'openai':
        return 'https://api.openai.com';
      case 'openai_compatible':
        return '';
      default:
        return '';
    }
  };

  const getDefaultConfig = (type: string) => {
    switch (type) {
      case 'anthropic':
        return { version: '2023-06-01', max_retries: 3 };
      case 'openai':
        return { organization: '', max_retries: 3 };
      case 'openai_compatible':
        return { site_url: '', app_name: 'LLM Router' };
      default:
        return {};
    }
  };

  const renderConfigFields = () => {
    switch (data.type) {
      case 'anthropic':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                API Version
              </label>
              <input
                type="text"
                value={data.config.version || '2023-06-01'}
                onChange={(e) => setData('config', { ...data.config, version: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Max Retries
              </label>
              <input
                type="number"
                value={data.config.max_retries || 3}
                onChange={(e) => setData('config', { ...data.config, max_retries: parseInt(e.target.value) })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        );
      case 'openai':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Organization ID (Optional)
              </label>
              <input
                type="text"
                value={data.config.organization || ''}
                onChange={(e) => setData('config', { ...data.config, organization: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="org-..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Max Retries
              </label>
              <input
                type="number"
                value={data.config.max_retries || 3}
                onChange={(e) => setData('config', { ...data.config, max_retries: parseInt(e.target.value) })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        );
      case 'openai_compatible':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Site URL
              </label>
              <input
                type="url"
                value={data.config.site_url || ''}
                onChange={(e) => setData('config', { ...data.config, site_url: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="https://your-app.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                App Name
              </label>
              <input
                type="text"
                value={data.config.app_name || 'LLM Router'}
                onChange={(e) => setData('config', { ...data.config, app_name: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const breadcrumbItems = [
    { name: 'Providers', href: '/admin/providers' },
    { name: 'Create Provider', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center justify-between w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Create Provider
            </h1>
          </div>
          <Link
            href="/admin/providers"
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Providers
          </Link>
        </div>
      }
    >
      <Head title="Create Provider" />

      <div className="max-w-2xl mx-auto">
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Name *
                </label>
                <input
                  type="text"
                  value={data.name}
                  onChange={(e) => setData('name', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Type *
                </label>
                <select
                  value={data.type}
                  onChange={(e) => handleTypeChange(e.target.value as any)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="openai_compatible">OpenAI Compatible</option>
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Base URL *
                </label>
                <input
                  type="url"
                  value={data.base_url}
                  onChange={(e) => setData('base_url', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
                {errors.base_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.base_url}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  API Key *
                </label>
                <input
                  type="password"
                  value={data.api_key}
                  onChange={(e) => setData('api_key', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
                {errors.api_key && (
                  <p className="mt-1 text-sm text-red-600">{errors.api_key}</p>
                )}
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={data.is_active}
                  onChange={(e) => setData('is_active', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                  Active
                </label>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                {renderConfigFields()}
              </div>

              <div className="flex items-center justify-end space-x-4">
                <Link
                  href="/admin/providers"
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={processing}
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                >
                  {processing ? 'Creating...' : 'Create Provider'}
                </button>
              </div>
            </form>
        </div>
      </div>
    </AdminLayout>
  );
}
