import React from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import DataTable from '@/Components/Admin/DataTable';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

interface Provider {
  id: string;
  name: string;
  type: 'anthropic' | 'openai' | 'openai_compatible';
  base_url: string;
  is_active: boolean;
  created_at: string;
  llm_models: Array<{
    id: string;
    name: string;
    is_active: boolean;
  }>;
}

interface ProvidersIndexProps {
  providers: Provider[];
}

export default function Index({ providers }: ProvidersIndexProps) {
  const handleDelete = (provider: Provider) => {
    if (confirm(`Are you sure you want to delete ${provider.name}?`)) {
      router.delete(`/admin/providers/${provider.id}`);
    }
  };

  const handleTestConnection = async (provider: Provider) => {
    try {
      const response = await fetch(`/admin/providers/${provider.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': (document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement)?.content || '',
        },
      });

      const result = await response.json();

      if (result.success) {
        alert('Connection test successful!');
      } else {
        alert('Connection test failed: ' + result.message);
      }
    } catch (error) {
      alert('Connection test failed: ' + error);
    }
  };

  const getProviderTypeBadge = (type: string) => {
    const badges = {
      anthropic: 'bg-purple-100 text-purple-800',
      openai: 'bg-green-100 text-green-800',
      openai_compatible: 'bg-blue-100 text-blue-800',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[type as keyof typeof badges]}`}>
        {type.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const columns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      render: (value: string, row: Provider) => (
        <div className="flex items-center">
          <div>
            <div className="text-sm font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{row.base_url}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
      render: (value: string) => getProviderTypeBadge(value),
    },
    {
      key: 'is_active',
      label: 'Status',
      sortable: true,
      render: (value: boolean) => (
        <div className="flex items-center">
          {value ? (
            <>
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-800">Active</span>
            </>
          ) : (
            <>
              <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      key: 'llm_models',
      label: 'Models',
      render: (value: Provider['llm_models']) => (
        <div className="text-sm text-gray-900">
          {value.length} model{value.length !== 1 ? 's' : ''}
          {value.length > 0 && (
            <div className="text-xs text-gray-500">
              {value.filter(m => m.is_active).length} active
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: Provider) => (
        <div className="flex items-center space-x-2">
          <Link
            href={`/admin/providers/${row.id}`}
            className="text-blue-600 hover:text-blue-900"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </Link>
          <Link
            href={`/admin/providers/${row.id}/edit`}
            className="text-indigo-600 hover:text-indigo-900"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </Link>
          <button
            onClick={() => handleTestConnection(row)}
            className="text-green-600 hover:text-green-900"
            title="Test Connection"
          >
            <PlayIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { name: 'Providers', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Providers
            </h1>
          </div>
          <Link
            href="/admin/providers/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Provider
          </Link>
        </div>
      }
    >
      <Head title="Providers" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
              <DataTable
                columns={columns}
                data={providers}
                searchPlaceholder="Search providers..."
                emptyMessage="No providers found. Create your first provider to get started."
              />
        </div>
      </div>
    </AdminLayout>
  );
}
