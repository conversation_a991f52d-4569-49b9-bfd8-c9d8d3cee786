import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import DataTable from '@/Components/Admin/DataTable';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  is_active: boolean;
  max_tokens: number;
  cost_per_input_token: number;
  cost_per_output_token: number;
  capabilities: string[];
  created_at: string;
  provider: {
    id: string;
    name: string;
    type: string;
  };
}

interface ModelsIndexProps {
  models: LlmModel[];
}

export default function Index({ models }: ModelsIndexProps) {
  const handleDelete = (model: LlmModel) => {
    if (confirm(`Are you sure you want to delete ${model.name}?`)) {
      router.delete(`/admin/models/${model.id}`);
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Model',
      sortable: true,
      render: (value: string, row: LlmModel) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{row.model_identifier}</div>
        </div>
      ),
    },
    {
      key: 'provider',
      label: 'Provider',
      sortable: true,
      render: (value: LlmModel['provider']) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{value.name}</div>
          <div className="text-sm text-gray-500">{value.type}</div>
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      sortable: true,
      render: (value: boolean) => (
        <div className="flex items-center">
          {value ? (
            <>
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-800">Active</span>
            </>
          ) : (
            <>
              <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      key: 'cost_per_input_token',
      label: 'Input Cost',
      sortable: true,
      render: (value: number) => `$${(value * 1000).toFixed(3)}/1K`,
    },
    {
      key: 'cost_per_output_token',
      label: 'Output Cost',
      sortable: true,
      render: (value: number) => `$${(value * 1000).toFixed(3)}/1K`,
    },
    {
      key: 'max_tokens',
      label: 'Max Tokens',
      sortable: true,
      render: (value: number) => value?.toLocaleString() || 'N/A',
    },
    {
      key: 'capabilities',
      label: 'Capabilities',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value?.slice(0, 3).map((cap, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
            >
              {cap}
            </span>
          ))}
          {value?.length > 3 && (
            <span className="text-xs text-gray-500">+{value.length - 3} more</span>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: LlmModel) => (
        <div className="flex items-center space-x-2">
          <Link
            href={`/admin/models/${row.id}`}
            className="text-blue-600 hover:text-blue-900"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </Link>
          <Link
            href={`/admin/models/${row.id}/edit`}
            className="text-indigo-600 hover:text-indigo-900"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </Link>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { name: 'Models', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              LLM Models
            </h1>
          </div>
          <Link
            href="/admin/models/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Model
          </Link>
        </div>
      }
    >
      <Head title="LLM Models" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
              <DataTable
                columns={columns}
                data={models}
                searchPlaceholder="Search models..."
                emptyMessage="No models found. Add models to your providers to get started."
              />
        </div>
      </div>
    </AdminLayout>
  );
}
